/**
 * API 接口配置和调用工具
 * 统一管理小程序与后端的API对接
 */

const app = getApp();

// API 配置
const API_CONFIG = {
  // 后端服务地址
  BASE_URL: 'http://localhost:3000/api/',
  
  // 超时时间
  TIMEOUT: 30000,
  
  // 请求头
  HEADERS: {
    'Content-Type': 'application/json'
  }
};

/**
 * 统一的网络请求方法
 * @param {Object} options 请求配置
 * @param {String} options.url 接口路径
 * @param {String} options.method 请求方法
 * @param {Object} options.data 请求数据
 * @param {Object} options.header 请求头
 * @param {Function} options.success 成功回调
 * @param {Function} options.fail 失败回调
 */
function request(options) {
  const { url, data = {}, method = 'GET', header = {}, success, fail } = options;
  
  // 添加token到header
  const token = wx.getStorageSync('token');
  if (token) {
    header['Authorization'] = `Bearer ${token}`;
  }
  
  return wx.request({
    url: API_CONFIG.BASE_URL + url,
    data: data,
    method: method,
    timeout: API_CONFIG.TIMEOUT,
    header: {
      ...API_CONFIG.HEADERS,
      ...header
    },
    success: (res) => {
      console.log('API请求成功:', url, res.data);
      
      // 适配响应格式
      if (res.data.success) {
        // 转换为原格式以兼容现有代码
        const adaptedRes = {
          ...res,
          data: {
            code: 200,
            data: res.data.data,
            message: res.data.message
          }
        };
        success && success(adaptedRes);
      } else {
        // 错误处理
        const adaptedRes = {
          ...res,
          data: {
            code: 404,
            data: res.data.error || res.data.message,
            message: res.data.message
          }
        };
        success && success(adaptedRes);
      }
    },
    fail: (error) => {
      console.error('API请求失败:', url, error);
      fail && fail(error);
    }
  });
}

/**
 * 文件上传方法
 * @param {Object} options 上传配置
 * @param {String} options.filePath 文件路径
 * @param {String} options.name 文件字段名
 * @param {Object} options.header 请求头
 * @param {Function} options.success 成功回调
 * @param {Function} options.fail 失败回调
 */
function uploadFile(options) {
  const { filePath, name = 'image', header = {}, success, fail } = options;
  
  // 添加token到header
  const token = wx.getStorageSync('token');
  if (token) {
    header['Authorization'] = `Bearer ${token}`;
  }
  
  return wx.uploadFile({
    url: API_CONFIG.BASE_URL + 'image/upload',
    filePath: filePath,
    name: name,
    timeout: API_CONFIG.TIMEOUT,
    header: {
      'Content-Type': 'multipart/form-data',
      ...header
    },
    success: (res) => {
      console.log('文件上传成功:', res.data);
      
      try {
        const data = JSON.parse(res.data);
        if (data.success) {
          // 转换为原格式
          const adaptedRes = {
            ...res,
            data: JSON.stringify({
              code: 200,
              data: data.data.url, // 返回图片URL
              message: data.message
            })
          };
          success && success(adaptedRes);
        } else {
          const adaptedRes = {
            ...res,
            data: JSON.stringify({
              code: 404,
              data: data.error || data.message,
              message: data.message
            })
          };
          success && success(adaptedRes);
        }
      } catch (error) {
        console.error('解析上传响应失败:', error);
        fail && fail(error);
      }
    },
    fail: (error) => {
      console.error('文件上传失败:', error);
      fail && fail(error);
    }
  });
}

// API 接口定义
const API = {
  // 图片相关
  image: {
    upload: (filePath, options = {}) => uploadFile({
      filePath,
      ...options
    })
  },
  
  // AI 处理相关
  ai: {
    // 证件照制作
    createIdPhoto: (imageUrl, userId, options = {}) => request({
      url: 'ai/id-photo-creation',
      method: 'POST',
      data: { imageUrl, userId },
      ...options
    }),
    
    // 人像增强
    enhancePortrait: (imageUrl, userId, options = {}) => request({
      url: 'ai/portrait-enhancement',
      method: 'POST',
      data: { imageUrl, userId },
      ...options
    }),
    
    // 图片上色
    colorizeImage: (imageUrl, userId, options = {}) => request({
      url: 'ai/image-colorization',
      method: 'POST',
      data: { imageUrl, userId },
      ...options
    }),
    
    // 图片放大
    upscaleImage: (imageUrl, scale, userId, options = {}) => request({
      url: 'ai/image-upscaling',
      method: 'POST',
      data: { imageUrl, scale, userId },
      ...options
    }),
    
    // 图片翻译
    translateImage: (imageUrl, targetLanguage, userId, options = {}) => request({
      url: 'ai/image-translation',
      method: 'POST',
      data: { imageUrl, targetLanguage, userId },
      ...options
    }),
    
    // 获取处理记录
    getRecords: (params = {}, options = {}) => request({
      url: 'ai/records',
      method: 'GET',
      data: params,
      ...options
    })
  },
  
  // 系统相关
  system: {
    // 健康检查
    healthCheck: (options = {}) => request({
      url: 'health',
      method: 'GET',
      ...options
    })
  }
};

// 导出
module.exports = {
  API_CONFIG,
  request,
  uploadFile,
  API
};
