const app = getApp()
Page({
  data: {
    //轮播图配置
    autoplay: true,
    interval: 3000,
    duration: 1200,
    swiperHeight: 200,
    detail: {},
    isBeautyOn: 0,
    openIsBeautyOn: 0,
    safeAreaBottom: 0
  },


  onLoad: function (options) {
    const sizeDetail = JSON.parse(decodeURIComponent(options.data))
    this.setData({
      detail: sizeDetail,
      "detail.category":decodeURIComponent(options.category)
    })
    const data = {
      "swiperDatas": [{
          "id": 1,
          "imgurl": "./needs/1.jpg"
        },
        {
          "id": 2,
          "imgurl": "./needs/2.jpg"
        },
        {
          "id": 3,
          "imgurl": "./needs/3.jpg"
        }
      ]
    };
    
    // 获取窗口信息设置合适的轮播图大小
    const windowInfo = wx.getWindowInfo()
    // 根据屏幕宽度等比例设置高度
    const swiperHeight = windowInfo.windowWidth * 0.5 // 设置为屏幕宽度的一半
    // 解决小设备
    const safeAreaBottom = windowInfo.screenHeight - windowInfo.safeArea.bottom

    this.setData({
      swiperDatas: data.swiperDatas,
      swiperHeight: swiperHeight,
      safeAreaBottom: safeAreaBottom
    })
  },

  onShow: function () {
    this.getWebGlow();
  },

  //温馨提示
  warm() {
    wx.showToast({
      title: "请点击底部相册选择或相机拍照",
      icon: 'none',
      duration: 1500
    });
  },

  // 美颜开关切换
  onBeautySwitch(e) {
    this.setData({
      isBeautyOn: e.detail.value ? 1 : 0
    })
  },

  //获取管理员是否开启美颜
  getWebGlow() {
    // TODO: 需要在后端添加美颜配置API
    // 暂时默认开启美颜功能
    this.setData({
      openIsBeautyOn: true
    });

    /*
    // 未来的API调用：
    app.request({
      url: 'config/beauty-settings',
      method: 'GET',
      success: (res) => {
        if (res.data.code == 200) {
          this.setData({
            openIsBeautyOn: res.data.data.enabled
          });
        }
      },
      fail: (error) => {
        console.error('获取美颜配置失败:', error);
        // 默认开启
        this.setData({
          openIsBeautyOn: true
        });
      }
    });
    */
  },

  // 相册选择
  chooseImage() {
    if (wx.getStorageSync("token") == "") {
      wx.navigateTo({
        url: '/pages/login/index',
      });
      return;
    }
    wx.chooseMedia({
      count: 1,
      mediaType: 'image',
      sourceType: ['album'],
      sizeType: 'original',
      camera: 'back',
      success: (res) => {
        const file = res.tempFiles[0];
        const fileType = file.tempFilePath.split('.').pop().toLowerCase();
        const fileSizeMB = file.size / (1024 * 1024);

        // 检查文件格式
        if (fileType !== 'png' && fileType !== 'jpg' && fileType !== 'jpeg') {
          wx.showToast({
            title: '不支持该图片格式',
            icon: 'none',
            duration: 2000
          });
          return;
        }

        // 检查文件大小
        if (fileSizeMB > 15) {
          wx.showToast({
            title: '图片太大啦，不能超15M哦',
            icon: 'none',
            duration: 2000
          });
          return;
        }
        this.imgUpload(res.tempFiles[0].tempFilePath)
      }
    })
  },

  // 相机拍照
  chooseCamera() {
    if (wx.getStorageSync("token") == "") {
      wx.navigateTo({
        url: '/pages/login/index',
      });
      return;
    }
    const {
      category,
      heightMm,
      heightPx,
      id,
      name,
      widthMm,
      widthPx
    } = this.data.detail
    const isBeautyOn = this.data.isBeautyOn
    //选择相机拍照
    wx.getSetting({
      success(res) {
        if (res.authSetting['scope.camera']) {
          wx.navigateTo({
            url: '/pages/camera/index',
            success: function (res) {
              res.eventChannel.emit('chooseCamera', {
                category,
                heightMm,
                heightPx,
                id,
                name,
                widthMm,
                widthPx,
                isBeautyOn: isBeautyOn
              })
            }
          })
        } else {
          wx.authorize({
            scope: 'scope.camera',
            success() {},
            fail() {
              that.openConfirm()
            }
          })
        }
      },
      fail() {}
    })
  },

  // 开启摄像头
  openConfirm() {
    wx.showModal({
      content: '检测到您没打开访问摄像头权限，是否打开？',
      confirmText: "确认",
      cancelText: "取消",
      success: function (res) {
        console.log(res);
        if (res.confirm) {
          wx.openSetting({
            success: (res) => {}
          })
        }
      }
    });
  },

  // 上传原图
  imgUpload(filePath) {
    wx.showLoading({
      title: '图片检测中',
    })

    // 使用新的上传方法
    app.uploadFile({
      filePath: filePath,
      name: 'image',
      success: (res) => {
        wx.hideLoading();
        let data = JSON.parse(res.data);
        if (data.code == 200) {
          this.imageDivision(data.data);
        } else if (data.code == 404) {
          wx.showToast({
            title: data.data,
            icon: "none",
          });
        } else {
          wx.navigateTo({
            url: '/pages/login/index',
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        wx.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        });
      }
    })


  },

  imageDivision(imageUrl) {
    wx.showLoading({
      title: '制作中...',
    });

    // 使用新的证件照制作API
    app.request({
      url: 'ai/id-photo-creation',
      method: 'POST',
      data: {
        imageUrl: imageUrl,
        userId: wx.getStorageSync('userId') || 'anonymous'
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.code == 200) {
          // 适配返回数据格式
          const resultData = {
            kimg: res.data.data.segmentedImageUrl, // 抠图后的图片
            oimg: imageUrl, // 原图
            id2: Date.now(), // 临时ID
            ...this.data.detail
          };
          this.goEditPage(resultData);
        } else if (res.data.code == 404) {
          console.log(res.data);
          wx.showToast({
            title: res.data.data,
            icon: 'none'
          });
        } else {
          wx.navigateTo({
            url: '/pages/login/index',
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        wx.showToast({
          title: '制作失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 制作页面
  goEditPage(data) {
    const {
      category,
      heightMm,
      heightPx,
      id,
      name,
      widthMm,
      widthPx,
    } = this.data.detail
    const isBeautyOn = this.data.isBeautyOn
    wx.navigateTo({
      url: '/pages/edit/index',
      success: (res) => {
        res.eventChannel.emit('sendImageData', {
          ...data,
          category,
          heightMm,
          heightPx,
          id,
          name,
          widthMm,
          widthPx,
          isBeautyOn: isBeautyOn
        })
      }
    })
  }



})