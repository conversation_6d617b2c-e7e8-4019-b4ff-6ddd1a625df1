/* 页面布局样式 */
.wrap {
  height: 100vh;
  position: relative;
  width: 750rpx;
}

/* 顶部内容样式 */
.effect-top-bar {
  flex-direction: row;
  height: 140rpx;
  width: 100%;
}

.effect-top-bar,
.top-bar-icon {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}

.top-bar-icon {
  border-radius: 60rpx;
  box-shadow: 2px 2px 5px #a2a2a2;
  height: 80rpx;
  justify-content: center;
  margin-left: 40rpx;
  width: 80rpx;
}

.top-bar-title {
  color: #000;
  font-size: 40rpx;
}

.other-msg {
  display: inline-block;
  margin-left: 180rpx;
}

.card-stats {
  font-size: 21rpx;
  color: #fff;
  background: rgba(0, 0, 0, 0.5);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}




/* 图片展示样式 */
.effect-image {
  border-radius: 40rpx;
  height: 38vh;
  margin: 0 20rpx;
  width: 710rpx;
  overflow: hidden;
}

.effect-img {
  border-radius: 50rpx;
  height: 280px;
  width: 100%;
}

/* 内容样式 */
.effect-content {
  height: 38vh;
  overflow: scroll;
  padding: 20rpx 40rpx;
  width: 750rpx;
}

.content-view-a {
  display: -webkit-flex;
  display: flex;
  flex-direction: row;
  margin-top: 20rpx;
}

.view-icon-a,
.view-text-a {
  color: #000;
  font-size: 30rpx;
}

.view-text-a {
  font-weight: 700;
  margin-left: 12rpx;
}

.content-view {
  display: -webkit-flex;
  display: flex;
  flex-direction: row;
  margin-top: 2.8vh;
}

.view-text {
  color: #606266;
  font-size: 26rpx;
  line-height: 45rpx;
  margin-left: 15rpx;
  flex: 1;
  word-wrap: break-word;
}

/* 按钮样式 */
.effect-button {
  bottom: 40rpx;
  flex-direction: row;
  justify-content: space-around;
  position: absolute;
  width: 100%;
  align-items: center;
  display: -webkit-flex;
  display: flex;
}

.album-button {
  align-items: center;
  background-color: #2c2c2c;
  border-radius: 30rpx;
  color: #fff;
  display: -webkit-flex;
  display: flex;
  font-size: 35rpx;
  font-weight: 700;
  height: 120rpx;
  justify-content: center;
  width: 540rpx;
}

.album-button:active {
  opacity: 0.7;
}

/* 屏蔽层样式 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* 半透明背景 */
  z-index: 10;
}

/* 底部弹框样式 */
.bottom-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60%;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  box-shadow: 0 -6px 20px rgba(0, 0, 0, 0.2);
  z-index: 20;
  transform: translateY(100%); /* 默认隐藏 */
  display: flex;
  flex-direction: column;
}

.modal-content {
  padding: 30rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center; /* 居中对齐 */
  line-height: 60rpx; /* 增加行高 */
  margin-bottom: 20rpx;
}

.modal-row {
  margin-bottom: 25rpx;
}

.next-button {
  background-color: #1a1a1a;
  color: #fff;
  text-align: center;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin: 40rpx 30rpx;
  margin-top: auto;
}
