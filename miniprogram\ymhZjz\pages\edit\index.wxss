.radio-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.render-radio-group {
  display: flex;
  flex-direction: column; /* 改为垂直排列 */
  align-items: flex-start;
}

.radio-label {
  display: flex;
  align-items: center;
  margin-bottom: 10px; /* 改为垂直间距 */
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}

.radio-label radio {
  margin-right: 8px;
}

@media screen and (max-width: 375px) {
  .radio-label {
    margin-right: 10px;
    font-size: 12px;
  }
}

.container {
  width: 100vw;
  height: 90vh;
  overflow: hidden;
  position: relative;
}

.imgOpt {
  width: 100%;
  height: 100%;
  background: #fff;
}

.imgOpt .colors {
  z-index: 20;
  position: relative;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin-top: 10px;
  margin-left: 10px;
  overflow-x: hidden;
  touch-action: pan-y;
}

.imgOpt .colors .color {
  width: 22%;
  height: 12vw;
  margin: 1%;
  border: 1px solid #ddd;
  border-radius: 10px;
  overflow: hidden;
  box-sizing: border-box;
}

.imgOpt .colors .color.transparent {
  background-color: transparent;
  position: relative;
}

.imgOpt .colors .color.transparent::before {
  content: '透明';
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  text-align: center;
  color: #333;
  line-height: 1;
  height: 1em;
  margin-top: -0.5em;
  font-size: 14px;
}

.container .photo-edit-content {
  padding: 20px 10px 15px 10px;
  width: 100%;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAQBAMAAAD+CqKmAAAAKlBMVEX5+fn6+vr7+/v8/Pz////4+Pj39/f29vb19fX09PTz8/Py8vLx8fHo6OgTp668AAAAkElEQVR4XmO4iwQYrriAAIRkuKLRuXXO6itLp3UtjWa4Mn3i1l0dV6Slqy0TGK6I7dgtFnwlzXKp0mqGK61lkpXSV1Z0bty4neFKeljosg1XZq0IsBFkuCKxUJOB84pwgHgWO6o9EEsgJEPr9t1wqxgOaYTDrWLgLG2GW8WQJcYDt4phQvkZuFUM26uWwq0CAHdtZDNiQoOHAAAAAElFTkSuQmCC") ;
  background-size: auto;
}

.container .photo-edit-content .canvas-view {
  position: relative;
  width: 100%;
  height: 300px;
  margin: 0 auto;
  box-sizing: content-box;
  overflow: hidden;
  border-radius: 10px;
}

.container .photo-edit-content .canvas-view .people-photo {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform-origin: center center;
}

.download-view {
  z-index: 10;
  margin: 0 auto;
  width: 90%;
  position: fixed;
  bottom: 10px;
  display: flex;
  justify-content: center;
  left: 5%;
  height: auto;
}

.button-group {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.button-group .save-btn {
  width: 48%;
}

.button-group .single-btn {
  width: 100%;
}

.download-view button {
  background-color: #2c2c2c !important;
  color: #fff !important;
  font-weight: normal;
  height: 50px;
  line-height: 50px;
  border-radius: 10px;
  margin: 0;
}

.advanced-params {
  padding: 0px 20px;
}

.params-wrapper {
  display: flex;
  justify-content: space-between;
  gap: 20px; /* 添加间距 */
  flex-wrap: wrap; /* 允许换行 */
}

.left-params, .right-params {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 150px; /* 最小宽度 */
}

.right-params {
  /* 可根据需要调整右侧宽度 */
}

.param-item {
  display: flex;
  align-items: center;
  margin-bottom: 9px;
}

.param-label {
  flex: 0 0 100px; /* 调整标签宽度 */
  margin-right: 10px;
  font-size: 16px;
  color: #333;
}

.param-input {
  flex: 1;
  height: 40px;
  padding: 5px 10px;
  font-size: 16px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.radio-container {
  width: 100%;
}

.color-picker-view {
  position: absolute;
  width: 100%;
  overflow: visible;
  z-index: 99;
}

.color-picker-view .color-picker-content {
  height: 55vh;
  width: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.color-picker-view .color-picker-content .current-color {
  width: 100%;
}

.color-picker {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.color-picker-view .color-picker-content button {
  margin: 0 auto;
  background-color: #2c2c2c;
  color: #fff;
  margin-top: 10px;
}

.nowx_mask {
  margin: auto;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
