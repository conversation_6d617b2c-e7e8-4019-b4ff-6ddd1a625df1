.works {
  padding: 20rpx;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300rpx, 1fr));
  gap: 20rpx;
}

.photo-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
  overflow: hidden;
}

.photo-preview {
  width: 100%;
  height: 400rpx;
  overflow: hidden;
  position: relative;
}

.photo-preview image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.photo-preview image:active {
  transform: scale(1.02);
}

.photo-info {
  padding: 24rpx;
}

.photo-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.photo-size {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.photo-time {
  font-size: 28rpx;
  color: #999;
}

.photo-actions {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 24rpx 24rpx;
  gap: 20rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.empty-state image {
  width: 400rpx;
  height: 400rpx;
  margin-bottom: 40rpx;
}

.empty-state text {
  font-size: 32rpx;
  color: #999;
}