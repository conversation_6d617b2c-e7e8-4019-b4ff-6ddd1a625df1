.custom {
  min-height: 83vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.container {
  width: 90%;
  max-width: 500px;
  background-color: #fff;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.input-field {
  margin-bottom: 15px;
}

.bottom {
  width: 90%;
  max-width: 500px;
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.save-button {
  width: 100%;
  padding: 12px 0;
  background-color: #2c2c2c;
  color: #fff;
  border: none;
  border-radius: 10px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

/* 使用微信小程序的 hover-class 进行样式修改 */
.save-button-hover {
  background-color: #1a1a1a;
}

/* 添加 active 状态的反馈 */
.save-button:active {
  background-color: #0d0d0d;
}


/* 标题样式 */
.title {
  font-size: 30px;
  font-weight: bold;
  color: #333;
  margin-top: 20px;
  margin-bottom: 20px;
  text-align: center;
}

/* 响应式设计 */
@media screen and (max-width: 600px) {
  .container {
    width: 95%;
    padding: 15px;
  }

  .save-button {
    font-size: 16px;
  }
}
