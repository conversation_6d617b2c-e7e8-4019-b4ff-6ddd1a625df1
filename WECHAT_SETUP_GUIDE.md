# 微信小程序配置指南

## 🔧 **当前状态**

目前系统使用**测试模式**，跳过了微信API验证。这样可以让您在开发阶段正常测试所有功能。

## ⚠️ **错误说明**

您遇到的错误：
```json
{
  "errcode": 40029,
  "errmsg": "invalid code"
}
```

**原因**：微信登录code只能使用一次，且有5分钟有效期。在开发调试时经常会遇到这个问题。

## 🚀 **解决方案**

### **方案1：使用测试模式（推荐）**

我已经修改了后端代码，现在会跳过微信API验证，直接生成测试用户。

**优点**：
- ✅ 可以正常测试所有功能
- ✅ 不需要配置微信开发者账号
- ✅ 避免code重复使用问题

### **方案2：配置真实微信登录（生产环境）**

如果需要真实的微信登录，需要以下步骤：

#### **1. 微信小程序配置**
```javascript
// 在小程序中需要配置真实的AppID
// app.json
{
  "appid": "你的小程序AppID",
  "projectname": "证件照伴侣"
}
```

#### **2. 后端环境变量配置**
```bash
# backend/.env
WECHAT_APPID=你的小程序AppID
WECHAT_SECRET=你的小程序AppSecret
JWT_SECRET=你的JWT密钥
```

#### **3. 实现真实微信登录**
```javascript
// backend/routes/auth.js
const axios = require('axios');

async function getWechatUserInfo(code) {
  const response = await axios.get('https://api.weixin.qq.com/sns/jscode2session', {
    params: {
      appid: process.env.WECHAT_APPID,
      secret: process.env.WECHAT_SECRET,
      js_code: code,
      grant_type: 'authorization_code'
    }
  });
  
  return response.data;
}
```

## 🧪 **测试步骤**

### **当前测试模式下**

1. **重启后端服务**：
   ```bash
   cd backend
   npm run dev
   ```

2. **在小程序中测试登录**：
   - 点击登录按钮
   - 现在应该会显示"登录成功"
   - 用户信息会显示为"测试用户"

3. **验证功能**：
   - 登录后可以正常使用所有功能
   - 证件照制作
   - 作品保存
   - 数据统计

## 📱 **小程序开发配置**

### **开发阶段设置**

在微信开发者工具中：

1. **关闭域名校验**：
   - 详情 → 本地设置 → 不校验合法域名

2. **配置服务器域名**（可选）：
   - 开发管理 → 开发设置 → 服务器域名
   - request合法域名：`http://localhost:3000`

### **生产环境设置**

1. **配置合法域名**：
   - request合法域名：`https://你的域名.com`
   - uploadFile合法域名：`https://你的域名.com`

2. **开启域名校验**

## 🔄 **从测试模式切换到生产模式**

### **步骤1：获取微信小程序资质**
1. 注册微信小程序账号
2. 获取AppID和AppSecret
3. 配置服务器域名

### **步骤2：修改代码**
1. 在后端添加真实的微信API调用
2. 在小程序中配置真实的AppID
3. 添加环境变量配置

### **步骤3：测试验证**
1. 测试真实微信登录流程
2. 验证用户信息获取
3. 确保所有功能正常

## 💡 **开发建议**

### **当前阶段（开发测试）**
- ✅ 使用测试模式，专注于功能开发
- ✅ 完善AI处理功能
- ✅ 优化用户体验
- ✅ 测试所有业务流程

### **准备上线阶段**
- 🔄 申请微信小程序资质
- 🔄 配置真实登录
- 🔄 部署到生产环境
- 🔄 配置HTTPS域名

## 🎯 **立即可以测试的功能**

现在您可以正常测试：

1. **用户登录** - 使用测试模式登录
2. **证件照制作** - 完整的制作流程
3. **背景替换** - AI背景处理
4. **作品管理** - 保存和查看作品
5. **数据统计** - 管理后台统计功能

## 📞 **如果还有问题**

1. **重启后端服务**确保新代码生效
2. **清除小程序缓存**重新编译
3. **查看控制台日志**确认API调用
4. **检查网络连接**确保后端可访问

---

**🎉 现在您可以正常使用所有功能进行测试了！**
