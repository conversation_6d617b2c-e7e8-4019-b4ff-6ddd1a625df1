const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');

// 微信登录
router.post('/wechat-login', async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        error: '请提供微信登录code'
      });
    }

    console.log('收到微信登录请求，code:', code);

    // 暂时跳过微信API验证，直接生成用户信息
    // 在生产环境中需要调用微信API验证code
    const userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    const token = jwt.sign(
      { userId, loginTime: Date.now(), code },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );

    console.log('生成用户信息:', { userId, token: token.substring(0, 20) + '...' });

    res.json({
      success: true,
      data: {
        userId: userId,
        token: token,
        userInfo: {
          nickname: '测试用户',
          avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4uBMGjxKBdGrQQiapxHrfMR5W5HQiaixZQjOlZ2Cvfa2hqLVoEWTAJI7C3Lhbm9eMSMmdvg6VbXww/132'
        }
      },
      message: '登录成功'
    });

  } catch (error) {
    console.error('微信登录失败:', error);
    res.status(500).json({
      success: false,
      error: '登录失败',
      message: error.message
    });
  }
});

// 获取用户信息
router.get('/user-info', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        error: '未提供认证token'
      });
    }

    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    res.json({
      success: true,
      data: {
        userId: decoded.userId,
        loginTime: decoded.loginTime,
        userInfo: {
          nickname: '微信用户',
          avatar: ''
        }
      },
      message: '获取用户信息成功'
    });

  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(401).json({
      success: false,
      error: '认证失败',
      message: error.message
    });
  }
});

// 用户登出
router.post('/logout', async (req, res) => {
  try {
    // TODO: 可以在这里添加token黑名单逻辑
    
    res.json({
      success: true,
      message: '登出成功'
    });

  } catch (error) {
    console.error('登出失败:', error);
    res.status(500).json({
      success: false,
      error: '登出失败',
      message: error.message
    });
  }
});

module.exports = router;
