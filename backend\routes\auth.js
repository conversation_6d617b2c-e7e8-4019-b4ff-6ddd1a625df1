const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const wechatService = require('../services/wechatService');
const User = require('../models/User');

// 微信登录
router.post('/wechat-login', async (req, res) => {
  try {
    const { code, userInfo, encryptedData, iv } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        error: '请提供微信登录code'
      });
    }

    console.log('收到微信登录请求，code:', code);

    // 检查微信配置
    const configStatus = wechatService.getConfigStatus();
    if (!configStatus.configured) {
      console.log('微信配置未完成，使用测试模式');

      // 测试模式：生成临时用户
      const testUserId = 'test_user_' + Date.now();
      const token = jwt.sign(
        { userId: testUserId, openid: 'test_openid_' + Date.now(), loginTime: Date.now() },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '7d' }
      );

      return res.json({
        success: true,
        data: {
          userId: testUserId,
          token: token,
          userInfo: {
            nickname: '测试用户',
            avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4uBMGjxKBdGrQQiapxHrfMR5W5HQiaixZQjOlZ2Cvfa2hqLVoEWTAJI7C3Lhbm9eMSMmdvg6VbXww/132'
          },
          isTestMode: true
        },
        message: '登录成功（测试模式）'
      });
    }

    // 真实微信登录：调用微信API验证code
    const wechatResult = await wechatService.code2Session(code);

    if (!wechatResult.success) {
      console.error('微信API验证失败:', wechatResult.error);
      return res.status(400).json({
        success: false,
        error: wechatResult.error,
        code: wechatResult.code
      });
    }

    const { openid, session_key, unionid } = wechatResult.data;
    console.log('微信验证成功，openid:', openid);

    // 处理用户信息
    let userData = {
      openid,
      unionid
    };

    // 如果提供了加密的用户信息，进行解密
    if (encryptedData && iv && session_key) {
      const decryptResult = wechatService.decryptData(encryptedData, iv, session_key);
      if (decryptResult.success) {
        const decryptedUserInfo = decryptResult.data;
        userData = {
          ...userData,
          nickname: decryptedUserInfo.nickName,
          avatar: decryptedUserInfo.avatarUrl,
          gender: decryptedUserInfo.gender,
          country: decryptedUserInfo.country,
          province: decryptedUserInfo.province,
          city: decryptedUserInfo.city,
          language: decryptedUserInfo.language
        };
        console.log('用户信息解密成功');
      } else {
        console.warn('用户信息解密失败:', decryptResult.error);
      }
    } else if (userInfo) {
      // 使用明文用户信息（需要验证签名）
      if (userInfo.signature && session_key) {
        const isValid = wechatService.verifySignature(
          JSON.stringify(userInfo.rawData),
          userInfo.signature,
          session_key
        );

        if (isValid) {
          const rawData = JSON.parse(userInfo.rawData);
          userData = {
            ...userData,
            nickname: rawData.nickName,
            avatar: rawData.avatarUrl,
            gender: rawData.gender,
            country: rawData.country,
            province: rawData.province,
            city: rawData.city,
            language: rawData.language
          };
          console.log('用户信息签名验证成功');
        } else {
          console.warn('用户信息签名验证失败');
        }
      }
    }

    // 查找或创建用户
    const user = await User.findOrCreateByOpenid(openid, userData);
    console.log('用户处理完成:', user._id);

    // 生成JWT token
    const token = jwt.sign(
      {
        userId: user._id.toString(),
        openid: user.openid,
        loginTime: Date.now()
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );

    res.json({
      success: true,
      data: {
        userId: user._id.toString(),
        token: token,
        userInfo: {
          nickname: user.nickname,
          avatar: user.avatar,
          membershipType: user.membership.type
        },
        isTestMode: false
      },
      message: '登录成功'
    });

  } catch (error) {
    console.error('微信登录失败:', error);
    res.status(500).json({
      success: false,
      error: '登录失败',
      message: error.message
    });
  }
});

// 获取用户信息
router.get('/user-info', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        error: '未提供认证token'
      });
    }

    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

    // 如果是测试模式
    if (decoded.userId.startsWith('test_user_')) {
      return res.json({
        success: true,
        data: {
          userId: decoded.userId,
          loginTime: decoded.loginTime,
          userInfo: {
            nickname: '测试用户',
            avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4uBMGjxKBdGrQQiapxHrfMR5W5HQiaixZQjOlZ2Cvfa2hqLVoEWTAJI7C3Lhbm9eMSMmdvg6VbXww/132',
            membershipType: 'free'
          },
          isTestMode: true
        },
        message: '获取用户信息成功（测试模式）'
      });
    }

    // 真实用户：从数据库获取用户信息
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: {
        userId: user._id.toString(),
        openid: user.openid,
        loginTime: decoded.loginTime,
        userInfo: {
          nickname: user.nickname,
          avatar: user.avatar,
          gender: user.gender,
          country: user.country,
          province: user.province,
          city: user.city,
          membershipType: user.membership.type
        },
        stats: user.stats,
        preferences: user.preferences,
        isTestMode: false
      },
      message: '获取用户信息成功'
    });

  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(401).json({
      success: false,
      error: '认证失败',
      message: error.message
    });
  }
});

// 用户登出
router.post('/logout', async (req, res) => {
  try {
    // TODO: 可以在这里添加token黑名单逻辑

    res.json({
      success: true,
      message: '登出成功'
    });

  } catch (error) {
    console.error('登出失败:', error);
    res.status(500).json({
      success: false,
      error: '登出失败',
      message: error.message
    });
  }
});

// 检查微信配置状态
router.get('/wechat-config', async (req, res) => {
  try {
    const configStatus = wechatService.getConfigStatus();

    res.json({
      success: true,
      data: configStatus,
      message: '获取微信配置状态成功'
    });

  } catch (error) {
    console.error('获取微信配置状态失败:', error);
    res.status(500).json({
      success: false,
      error: '获取微信配置状态失败',
      message: error.message
    });
  }
});

// 更新用户信息
router.put('/user-info', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        error: '未提供认证token'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

    // 测试模式不支持更新
    if (decoded.userId.startsWith('test_user_')) {
      return res.status(400).json({
        success: false,
        error: '测试模式不支持更新用户信息'
      });
    }

    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    const { nickname, preferences } = req.body;

    // 更新用户信息
    if (nickname) user.nickname = nickname;
    if (preferences) {
      user.preferences = { ...user.preferences, ...preferences };
    }

    await user.save();

    res.json({
      success: true,
      data: user.getPublicInfo(),
      message: '用户信息更新成功'
    });

  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({
      success: false,
      error: '更新用户信息失败',
      message: error.message
    });
  }
});

module.exports = router;
