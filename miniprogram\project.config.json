{"miniprogramRoot": "ymhZjz/", "setting": {"urlCheck": true, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": false, "useCompilerModule": false, "userConfirmedUseCompilerModuleSwitch": false, "minifyWXML": true, "useMultiFrameRuntime": true, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"search": {"list": []}, "conversation": {"list": []}, "plugin": {"list": []}, "game": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}, "compileType": "miniprogram", "srcMiniprogramRoot": "ymhZjz/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "packOptions": {"ignore": [], "include": []}, "appid": "wx171a0e24682306b4", "projectname": "ymhZjz"}