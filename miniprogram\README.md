# 预览：

<p align="center"><img src="./assets/3.png"></p>

# 项目介绍

# <p align="center">证件照伴侣</p>
<p align="center">2025我们一起加油，心如所愿，梦想成真.</p>
<p align="center"><img src="./assets/1.png"></p>



**相关项目**：
- 小程序前端第二套：https://github.com/no1xuan/ai-photo
- 小程序前端第三套：https://github.com/no1xuan/id-photo-wechat
- 小程序后端：https://github.com/no1xuan/HivisionIDPhotos-wechat-weapp
- 小程序管理员网页后台：https://github.com/no1xuan/zjzAdmin

------

# ⭐最近更新
    版本更新教程：https://www.bilibili.com/video/BV1xNUvYTEjo

- 2024.END:   2024年最后一个版本，全新我的作品页面，增加图片编辑，修复所有的历史遗留包裹
- 2024.12.17: 固定微信基础库版本：3.7.0，管理员登录失败增加更详细信息，探索中心改动，登录状态检查，适配新版后端等
- 2024.12.06: 探索中心增加图片动漫风功能，修复定制列表没定制时没有弹出提示框的问题，删除200多个没用到的依赖文件
- 2024.11.29: 优化加快规格列表/搜索列表/照片列表，修复作品列表特殊场景删除造成不分页的问题，固定微信基础库版本：3.6.6
- 2024.11.22: 增加六寸排版照，相机增加返回，部分探索功能增加自定义参数，调整首页登录，我的作品增加点击图片查看，定制尺寸增加引导用户跳转制作，移除彩蛋遗留代码
- 2024.11.15: 大更新来喽！全局优化代码，页面UI优化，增加探索中心/通用抠图，修复搜索页无法一键到顶，调整我的作品登录逻辑，调整个人中心未登录点击逻辑，移除彩蛋
- 2024.11.08: 增加黑白图片上色功能
- 2024.11.05: 增加登录失败时控制台打印错误原因
- 2024.10.18: 修复高清照一直没生效的问题
- 2024.10.07: 增加管理员后台
- 2024.10.04: 修复高级参数dpi参数读取错误
- 2024.10.03: 修复自定义尺寸一直没生效的问题，修复部分传参错误
- 2024.10.02: 增加高级参数，美化页面，去除无用变量
- 2024.09.23: 增加个人中心，优化拍照技巧，优化定制尺寸，修复首点击部分功能会出现重复跳转2次的问题
- 2024.09.21: 修复我的作品无法下载问题
- 2024.09.20: 根据微信热力图优化体验，加大首页按钮点击范围，修复分页后删除作品页面没变化，增加温馨提示
- 2024.09.19: 增加看广告视频下载功能
- 2024.09.14: 根据群友建议缩短弹窗时间，修复白色背景生成出来是黑色的问题，修复离开小程序就掉登录的问题
- 2024.09.13: 根据微信热力图优化用户体验
- 2024.09.12: 第一个版本诞生
------

# 🔧部署

微信开发者工具打开项目后，修改两处就好啦

<img src="./assets/2.png">



## 隐私协议

<img src="./assets/4.png">



## 客服配置

<img src="./assets/10.png">



## 📧其它

您可以通过以下方式联系我:

QQ: 24677102

微信：webxuan
