<view class="custom">
  <view class="title">轻松定制自已专属尺寸</view>
  <view class="container">
    <van-cell-group>

      <!-- 名称输入框 -->
      <van-field 
        value="{{ name }}" 
        type="text" 
        label="名称：" 
        placeholder="请输入名称" 
        border 
        size="large" 
        maxlength="10" 
        input-align="right" 
        bind:input="changeName" 
        class="input-field"
      />

      <!-- 宽度输入框 -->
      <van-field 
        value="{{ width }}" 
        type="number" 
        label="宽度：" 
        placeholder="像素：px" 
        border 
        size="large" 
        maxlength="4" 
        input-align="right" 
        bind:input="changeWidth" 
        class="input-field"
      />

      <!-- 高度输入框 -->
      <van-field 
        value="{{ height }}" 
        type="number" 
        label="高度：" 
        placeholder="像素：px" 
        border 
        size="large" 
        maxlength="4" 
        input-align="right" 
        bind:input="changeHeight" 
        class="input-field"
      />

        <!-- 分辨率输入框 -->
        <van-field 
        value="{{ dpi }}" 
        type="number" 
        label="分辨率：" 
        placeholder="分辨率：dpi" 
        border 
        size="large" 
        maxlength="4" 
        input-align="right" 
        bind:input="changeDpi" 
        class="input-field"
      />

      <!-- 尺寸显示框 -->
      <van-field 
        value="{{ size }}" 
        type="text" 
        label="尺寸:" 
        placeholder="尺寸：mm" 
        border 
        size="large" 
        input-align="right" 
        readonly 
        class="input-field"
      />

      <!-- 像素显示框 -->
      <van-field 
        value="{{ px }}" 
        type="text" 
        label="像素:" 
        placeholder="像素：px" 
        border 
        size="large" 
        input-align="right" 
        readonly 
        class="input-field"
      />

    </van-cell-group>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom">
    <button bindtap="addSize" class="save-button" hover-class="save-button-hover">保存尺寸</button>
  </view>
  <!-- 弹窗 -->
  <van-dialog id="van-dialog" />
</view>
