App({
  // 修改为您的后端API地址
  url: "http://localhost:3000/api/",
  appName: "证件照伴侣",

  // 全局API调用方法
  request: function(options) {
    const { url, data = {}, method = 'GET', header = {}, success, fail } = options;

    // 添加token到header
    const token = wx.getStorageSync('token');
    if (token) {
      header['Authorization'] = `Bearer ${token}`;
    }

    return wx.request({
      url: this.url + url,
      data: data,
      method: method,
      header: {
        'Content-Type': 'application/json',
        ...header
      },
      success: (res) => {
        // 适配新的响应格式
        if (res.data.success) {
          // 转换为原格式以兼容现有代码
          const adaptedRes = {
            ...res,
            data: {
              code: 200,
              data: res.data.data,
              message: res.data.message
            }
          };
          success && success(adaptedRes);
        } else {
          // 错误处理
          const adaptedRes = {
            ...res,
            data: {
              code: 404,
              data: res.data.error || res.data.message,
              message: res.data.message
            }
          };
          success && success(adaptedRes);
        }
      },
      fail: fail
    });
  },

  // 文件上传方法
  uploadFile: function(options) {
    const { filePath, name = 'image', header = {}, success, fail } = options;

    // 添加token到header
    const token = wx.getStorageSync('token');
    if (token) {
      header['Authorization'] = `Bearer ${token}`;
    }

    return wx.uploadFile({
      url: this.url + 'image/upload',
      filePath: filePath,
      name: name,
      header: {
        'Content-Type': 'multipart/form-data',
        ...header
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          if (data.success) {
            // 转换为原格式
            const adaptedRes = {
              ...res,
              data: JSON.stringify({
                code: 200,
                data: data.data.url, // 返回图片URL
                message: data.message
              })
            };
            success && success(adaptedRes);
          } else {
            const adaptedRes = {
              ...res,
              data: JSON.stringify({
                code: 404,
                data: data.error || data.message,
                message: data.message
              })
            };
            success && success(adaptedRes);
          }
        } catch (error) {
          fail && fail(error);
        }
      },
      fail: fail
    });
  }
})