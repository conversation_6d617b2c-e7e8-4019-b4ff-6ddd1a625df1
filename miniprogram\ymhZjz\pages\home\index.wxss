/* 轮博图样式 */
.swiper-component {
  width: 100%;
  height: 60vh;
}

.swiper-content {
  width: 100%;
  height: 100%;
}

.slide-image {
  width: 100%;
  height: 100%;
}

/* 内容区域 */
.content {
  width: 90%;
  height: 400rpx;
  background-color: #fff;
  margin: 0 auto;
  border-radius: 30rpx;
  position: relative;
  top: -100rpx;
  box-shadow: 0px 5px 8px #ccc;
}

.top {
  height: 65%;
  border-bottom: 1rpx solid #eee;
}

.top-block {
  height: 100%;
  display: flex;
  align-items: center;
}

.top-left {
  width: 33.333%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  font-weight: 500;
}

.top-line {
  height: 80%;
  border: 1rpx solid #eee;
}

.top-right {
  width: 66.666%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  font-weight: 500;
}

.bottom {
  height: 35%;
  border-radius: 40rpx;
}

.bottom-block {
  height: 100%;
  display: flex;
  align-items: center;
}

.bottom-line {
  height: 70%;
  border: 1rpx solid #eee;
}

.bottom-cell {
  width: 33.3%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20rpx;
}

.bottom-cell>view view:last-child {
  font-size: 18rpx;
  color: #ccc;
}

.my-photo {
  width: 90%;
  height: 100rpx;
  background-color: #fff;
  margin: 0 auto;
  border-radius: 30rpx;
  position: relative;
  top: -70rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.my-photo view:first-child {
  display: flex;
  align-items: center;
  font-size: 14px;
}

/* 登录提示条样式 */
.login-prompt {
  width: 100%;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.6); /* 半透明黑色背景 */
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  padding: 0 20rpx;
  font-size: 26rpx;
  z-index: 1000;
}

/* 文字样式 */
.login-text {
  margin-left: 30rpx; /* 文字向后移动 */
  flex: 1;
}

/* 登录按钮样式 */
.login-button {
  padding: 8rpx 24rpx;
  background-color: #000000; /* 青蓝色 */
  color: #ffffff;
  border-radius: 50rpx;
  font-size: 24rpx;
  border: none;
  margin-right: 30rpx; /* 按钮向左靠近文字 */
  opacity: 0.9; /* 半透明按钮 */
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
}
