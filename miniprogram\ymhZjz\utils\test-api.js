/**
 * API 测试工具
 * 用于测试小程序与后端的API对接
 */

const app = getApp();

// 测试配置
const TEST_CONFIG = {
  // 测试图片URL (可以使用网络图片或本地图片)
  TEST_IMAGE_URL: 'https://example.com/test-image.jpg',
  
  // 测试用户ID
  TEST_USER_ID: 'test_user_123'
};

/**
 * 测试健康检查API
 */
function testHealthCheck() {
  console.log('🔍 测试健康检查API...');
  
  app.request({
    url: 'health',
    method: 'GET',
    success: (res) => {
      console.log('✅ 健康检查成功:', res.data);
      wx.showToast({
        title: '健康检查成功',
        icon: 'success'
      });
    },
    fail: (error) => {
      console.error('❌ 健康检查失败:', error);
      wx.showToast({
        title: '健康检查失败',
        icon: 'error'
      });
    }
  });
}

/**
 * 测试证件照制作API
 */
function testIdPhotoCreation() {
  console.log('🔍 测试证件照制作API...');
  
  app.request({
    url: 'ai/id-photo-creation',
    method: 'POST',
    data: {
      imageUrl: TEST_CONFIG.TEST_IMAGE_URL,
      userId: TEST_CONFIG.TEST_USER_ID
    },
    success: (res) => {
      console.log('✅ 证件照制作成功:', res.data);
      wx.showToast({
        title: '证件照制作成功',
        icon: 'success'
      });
    },
    fail: (error) => {
      console.error('❌ 证件照制作失败:', error);
      wx.showToast({
        title: '证件照制作失败',
        icon: 'error'
      });
    }
  });
}

/**
 * 测试人像增强API
 */
function testPortraitEnhancement() {
  console.log('🔍 测试人像增强API...');
  
  app.request({
    url: 'ai/portrait-enhancement',
    method: 'POST',
    data: {
      imageUrl: TEST_CONFIG.TEST_IMAGE_URL,
      userId: TEST_CONFIG.TEST_USER_ID
    },
    success: (res) => {
      console.log('✅ 人像增强成功:', res.data);
      wx.showToast({
        title: '人像增强成功',
        icon: 'success'
      });
    },
    fail: (error) => {
      console.error('❌ 人像增强失败:', error);
      wx.showToast({
        title: '人像增强失败',
        icon: 'error'
      });
    }
  });
}

/**
 * 测试图片上色API
 */
function testImageColorization() {
  console.log('🔍 测试图片上色API...');
  
  app.request({
    url: 'ai/image-colorization',
    method: 'POST',
    data: {
      imageUrl: TEST_CONFIG.TEST_IMAGE_URL,
      userId: TEST_CONFIG.TEST_USER_ID
    },
    success: (res) => {
      console.log('✅ 图片上色成功:', res.data);
      wx.showToast({
        title: '图片上色成功',
        icon: 'success'
      });
    },
    fail: (error) => {
      console.error('❌ 图片上色失败:', error);
      wx.showToast({
        title: '图片上色失败',
        icon: 'error'
      });
    }
  });
}

/**
 * 测试图片放大API
 */
function testImageUpscaling() {
  console.log('🔍 测试图片放大API...');
  
  app.request({
    url: 'ai/image-upscaling',
    method: 'POST',
    data: {
      imageUrl: TEST_CONFIG.TEST_IMAGE_URL,
      scale: 2,
      userId: TEST_CONFIG.TEST_USER_ID
    },
    success: (res) => {
      console.log('✅ 图片放大成功:', res.data);
      wx.showToast({
        title: '图片放大成功',
        icon: 'success'
      });
    },
    fail: (error) => {
      console.error('❌ 图片放大失败:', error);
      wx.showToast({
        title: '图片放大失败',
        icon: 'error'
      });
    }
  });
}

/**
 * 测试处理记录API
 */
function testGetRecords() {
  console.log('🔍 测试处理记录API...');
  
  app.request({
    url: 'ai/records',
    method: 'GET',
    data: {
      userId: TEST_CONFIG.TEST_USER_ID,
      page: 1,
      limit: 10
    },
    success: (res) => {
      console.log('✅ 获取处理记录成功:', res.data);
      wx.showToast({
        title: '获取记录成功',
        icon: 'success'
      });
    },
    fail: (error) => {
      console.error('❌ 获取处理记录失败:', error);
      wx.showToast({
        title: '获取记录失败',
        icon: 'error'
      });
    }
  });
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('🚀 开始运行所有API测试...');
  
  wx.showLoading({
    title: '正在测试API...'
  });
  
  // 依次测试各个API
  setTimeout(() => testHealthCheck(), 500);
  setTimeout(() => testIdPhotoCreation(), 1500);
  setTimeout(() => testPortraitEnhancement(), 2500);
  setTimeout(() => testImageColorization(), 3500);
  setTimeout(() => testImageUpscaling(), 4500);
  setTimeout(() => testGetRecords(), 5500);
  
  setTimeout(() => {
    wx.hideLoading();
    console.log('✅ 所有API测试完成');
    wx.showToast({
      title: '测试完成',
      icon: 'success'
    });
  }, 6500);
}

// 导出测试方法
module.exports = {
  testHealthCheck,
  testIdPhotoCreation,
  testPortraitEnhancement,
  testImageColorization,
  testImageUpscaling,
  testGetRecords,
  runAllTests
};
