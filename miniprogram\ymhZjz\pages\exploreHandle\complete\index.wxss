.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-top: 10rpx;
  display: block;
}

.image-container {
  width: 90%;
  height: 60vh;
  position: relative;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  overflow: hidden;
  margin: 20rpx 0;
}

.matting-container {
  background: #f0f0f0;
  background-image: linear-gradient(45deg, #ddd 25%, transparent 25%),
    linear-gradient(-45deg, #ddd 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #ddd 75%),
    linear-gradient(-45deg, transparent 75%, #ddd 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.result-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: rgba(76, 175, 80, 0.9);
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
}

.overlay-text {
  color: #fff;
  font-size: 24rpx;
}

.bottom {
  margin: 0 auto;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: 40px;
}

.bottom button:first-child {
  width: 40%;
  height: 55px;
  margin-left: 30px;
  background-color: #eee;
  border-radius: 10px;
  font-size: 18px;
  text-align: center;
  line-height: 55px;
}

.bottom button:last-child {
  width: 40%;
  height: 55px;
  margin-right: 30px;
  background-color: #2c2c2c;
  border-radius: 10px;
  color: #fff;
  font-size: 18px;
  text-align: center;
  line-height: 55px
}