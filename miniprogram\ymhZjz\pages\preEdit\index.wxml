<view class="preEdit">
  <view class="swiper-component">
    <swiper class="swiper-content" indicator-dots="true" autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}" circular>
      <block wx:for="{{swiperDatas}}" wx:key="id">
        <swiper-item>
          <image src="{{item.imgurl}}" class="slide-image" mode="aspectFill" />
        </swiper-item>
      </block>
    </swiper>
  </view>

  <view class="preEdit-content">
    <view bindtap="warm">
    <view class="title">{{detail.name}}</view>
    <view class="block">
      <view>冲印尺寸</view>
      <view>{{detail.widthMm}}*{{detail.heightMm}}</view>
    </view>
    <view class="block">
      <view>像素尺寸</view>
      <view>{{detail.widthPx}}*{{detail.heightPx}}</view>
    </view>
    <view class="block">
      <view>颜色可选</view>

      <view class="dec colorPick">
        <view class="circle" style="background: #fff; border:1px solid #ddd;"></view>
        <view class="circle" style="background: #438edb;"></view>
        <view class="circle" style="background: #00bff3;"></view>
        <view class="circle" style="background: #ff0000;"></view>
        <view class="circle rainbow"></view>
      </view>

    </view>
    </view>
    <view class="block beauty-switch" wx:if="{{openIsBeautyOn==1}}">
      <view>美颜开关</view>
      <switch checked="{{isBeautyOn==1}}" bindchange="onBeautySwitch" color="#2c2c2c" style="transform:scale(0.8)"/>
    </view>
  </view>

  <view class="bottom">
    <view bindtap="chooseImage">相册选择</view>
    <view bindtap="chooseCamera">相机拍照</view>
  </view>
</view>
