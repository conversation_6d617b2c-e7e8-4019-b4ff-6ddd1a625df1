<view class="explore-container">
  <view class="explore-header">
    <view class="explore-header-left">
      <text class="explore-header-title">探索神奇的魔力🪄 </text>
      <text class="explore-header-subtitle">让照片处理变得简单有趣✨ </text>
    </view>
  </view>

  <view class="explore-grid">
    <view class="explore-row">
      <view class="explore-card" bindtap="sizeListTo" data-type="3">
        <image class="card-image" mode="aspectFill" src="../../images/home/<USER>">
          <view class="other-msg">
            <view class="card-stats">✨ 推荐 | {{zjzCount}}次使用</view>
          </view>
        </image>
        <view class="card-content">
          <view class="card-title">智能证件照🎭</view>
          <view class="card-desc">支持多种尺寸+自定义背景</view>
        </view>
      </view>

      <view wx:if="{{generateLayoutCount!=-1}}" class="explore-card" bindtap="navigateTo" data-type="4">
        <image class="card-image" mode="aspectFill" src="../../images/home/<USER>">
          <view class="other-msg">
            <view class="card-stats">✨ 推荐 | {{generateLayoutCount}}次使用</view>    
          </view>
        </image>
        <view class="card-content">
          <view class="card-title">六寸排版照✨</view>
          <view class="card-desc">一键生成精美照片排版</view>
        </view>
      </view>
      
    </view>


    <view class="explore-row">

      <view wx:if="{{colourizeCount!=-1}}" class="explore-card" bindtap="navigateTo" data-type="5">
        <image class="card-image" mode="aspectFill" src="../../images/home/<USER>">
          <view class="other-msg">
            <view class="card-stats">🔥 推荐 | {{colourizeCount}}次使用</view>
          </view>
        </image>
        <view class="card-content">
        <view class="card-title">老照片上色🎨</view>
          <view class="card-desc">一键让黑白照片充满色彩</view>
        </view>
      </view>

      <view wx:if="{{mattingCount!=-1}}" class="explore-card" bindtap="navigateTo" data-type="6">
        <image class="card-image" mode="aspectFill" src="../../images/home/<USER>">
          <view class="other-msg">
            <view class="card-stats">🔥 热门 | {{mattingCount}}次使用</view>
          </view>
        </image>
        <view class="card-content">
         <view class="card-title">智能抠图✂️</view>
          <view class="card-desc">一键迅捷抠图，智能抠图</view>
        </view>
      </view>

    </view>


    <view class="explore-row">

      <view wx:if="{{editImageCount!=-1}}" class="explore-card" bindtap="navigateTo" data-type="7">
        <image class="card-image" mode="aspectFill" src="../../images/home/<USER>">
          <view class="other-msg">
            <view class="card-stats">🆕 新品 | {{editImageCount}}次使用</view>
          </view>
        </image>
        <view class="card-content">
          <view class="card-title">图片编辑✏️</view>
          <view class="card-desc">一键随心所欲编辑图片</view>
        </view>
      </view>

      <view wx:if="{{cartoonCount!=-1}}" class="explore-card" bindtap="navigateTo" data-type="8">
        <image class="card-image" mode="aspectFill" src="../../images/home/<USER>">
          <view class="other-msg">
            <view class="card-stats">🆕 新品 | {{cartoonCount}}次使用</view>
          </view>
        </image>
        <view class="card-content">
          <view class="card-title">新海诚动漫风🎨</view>
          <view class="card-desc">一键转换动漫风格照片</view>
        </view>
      </view>

    </view>



  </view>
</view>
