# 小程序API对接修改说明

## 📋 修改概述

已将小程序端的API调用从原有的第三方服务修改为与您的后端服务对接。

## 🔧 主要修改内容

### 1. 全局配置修改 (`app.js`)

**修改前:**
```javascript
App({
  url:"https://wx.0po.cn/",
  appName:"证件照伴侣"
})
```

**修改后:**
```javascript
App({
  url: "http://localhost:3000/api/",
  appName: "证件照伴侣",
  
  // 添加了统一的API调用方法
  request: function(options) { ... },
  uploadFile: function(options) { ... }
})
```

### 2. API接口映射

| 功能 | 原接口 | 新接口 | 状态 |
|------|--------|--------|------|
| 图片上传 | `upload` | `image/upload` | ✅ 已对接 |
| 证件照制作 | `api/createIdPhoto` | `ai/id-photo-creation` | ✅ 已对接 |
| 人像增强 | `api/createIdHdPhoto` | `ai/portrait-enhancement` | ✅ 已对接 |
| 图片上色 | - | `ai/image-colorization` | ✅ 已对接 |
| 图片放大 | - | `ai/image-upscaling` | ✅ 已对接 |
| 图片翻译 | - | `ai/image-translation` | ✅ 已对接 |
| 用户登录 | `user/login` | - | ⚠️ 待开发 |
| 背景替换 | `api/updateIdPhoto` | - | ⚠️ 待开发 |

### 3. 数据格式适配

**原格式:**
```json
{
  "code": 200,
  "data": "...",
  "message": "..."
}
```

**新格式:**
```json
{
  "success": true,
  "data": {...},
  "message": "..."
}
```

已在 `app.js` 中添加了格式转换逻辑，确保兼容性。

## 📁 修改的文件列表

1. **`app.js`** - 全局配置和API调用方法
2. **`pages/camera/index.js`** - 相机页面API调用
3. **`pages/preEdit/index.js`** - 预编辑页面API调用
4. **`pages/edit/index.js`** - 编辑页面API调用
5. **`pages/home/<USER>
6. **`pages/exploreHandle/index.js`** - 探索处理页面API调用
7. **`pages/explore/index.js`** - 探索页面数据统计
8. **`pages/searchs/index.js`** - 搜索页面API调用
9. **`pages/works/index.js`** - 作品列表页面API调用
10. **`utils/api.js`** - 新增API工具文件

## ⚠️ 待完成功能

### 1. 用户认证系统
```javascript
// 需要在后端添加的API
POST /api/auth/wechat-login
{
  "code": "微信登录code"
}
```

### 2. 背景替换功能
```javascript
// 需要在后端添加的API
POST /api/ai/background-replacement
{
  "imageUrl": "图片URL",
  "backgroundColor": "#ffffff",
  "userId": "用户ID"
}
```

### 3. 广告配置管理
```javascript
// 需要在后端添加的API
GET /api/config/advertisement
```

### 4. 证件照尺寸管理
```javascript
// 需要在后端添加的API
GET /api/photo-sizes/search
{
  "pageNum": 1,
  "pageSize": 10,
  "name": "搜索关键词"
}
```

### 5. 用户作品管理
```javascript
// 需要在后端添加的API
GET /api/user/works
{
  "pageNum": 1,
  "pageSize": 10,
  "userId": "用户ID"
}
```

### 6. 数据统计功能
```javascript
// 需要在后端添加的API
GET /api/statistics/explore-counts
```

### 7. 美颜配置管理
```javascript
// 需要在后端添加的API
GET /api/config/beauty-settings
```

## 🚀 使用说明

### 1. 启动后端服务
```bash
cd backend
npm run dev
```

### 2. 修改小程序配置
在微信开发者工具中，确保已配置合法域名：
- `http://localhost:3000` (开发环境)
- 或您的正式域名 (生产环境)

### 3. 测试功能
1. 图片上传 ✅
2. 证件照制作 ✅
3. 人像增强 ✅
4. 图片处理功能 ✅

## 🔄 临时处理方案

对于暂未实现的功能，已添加临时处理：

1. **登录功能**: 生成临时用户ID和token
2. **背景替换**: 显示"功能开发中"提示
3. **广告配置**: 禁用广告功能

## 📝 注意事项

1. **域名配置**: 生产环境需要配置HTTPS域名
2. **错误处理**: 已添加统一的错误处理机制
3. **兼容性**: 保持了原有的数据格式，确保界面正常显示
4. **日志记录**: 添加了详细的API调用日志

## 🎯 下一步计划

1. 完善用户认证系统
2. 实现背景替换功能
3. 添加更多AI处理功能
4. 优化错误处理和用户体验
5. 添加数据缓存机制

## 📞 技术支持

如有问题，请检查：
1. 后端服务是否正常运行
2. 网络连接是否正常
3. 控制台是否有错误信息
4. API接口是否返回正确格式
