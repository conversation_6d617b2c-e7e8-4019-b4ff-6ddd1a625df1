.search {
  height: 100vh;
  background-color: #fff;
}

.s-input {
  margin-top: 1vh;
  margin-left: 10px;
  margin-right: 10px;
}

.van-search__content {
  border: 1px solid #2c2c2c;
  background-color: #fff !important;
  color: #2c2c2c !important;
}

.container {
  margin-top: 15px;
  background-color: #f8f8f8;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 16px;
}

.grid-item {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.grid-image {
  width: 72px;
  height: 72px;
  margin-bottom: 12px;
}

.grid-content {
  text-align: center;
  width: 100%;
}

.none {
  width: 300px;
  height: 300px;
  margin: 0 auto;
}

.title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 6px;
  color: #2c2c2c;
}

.description {
  font-size: 12px;
  color: #888;
  line-height: 1.5;
}

.scroll-to-top {
  position: fixed;
  bottom: 64px;
  right: 24px;
  width: 48px;
  height: 48px;
  background-color: #4a95e0;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 12px rgba(74, 149, 224, 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.scroll-to-top:active {
  transform: scale(0.95);
  box-shadow: 0 1px 6px rgba(74, 149, 224, 0.2);
}
