{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@alicloud/pop-core": "^1.8.0", "ali-oss": "^6.23.0", "axios": "^1.9.0", "cors": "^2.8.5", "cos-nodejs-sdk-v5": "^2.15.0", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "multer": "^2.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}