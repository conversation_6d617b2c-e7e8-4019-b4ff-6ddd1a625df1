/* 通用样式重置 */
page {
  height: 100%;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 登录授权状态的卡片样式 */
.card {
  width: 90%;
  max-width: 400px;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.icon {
  margin-bottom: 20px;
}

.title {
  font-size: 18px;
  color: #333;
  margin-bottom: 20px;
}

.authorize-btn {
  width: 100%;
  height: 45px;
  line-height: 45px;
  background-color: #3CB371;
  color: white;
  border-radius: 8px;
  font-size: 16px;
  margin-bottom: 10px;
}

.cancel-btn {
  width: 100%;
  height: 45px;
  line-height: 45px;
  background-color: #e0e0e0;
  color: #666;
  border-radius: 8px;
  font-size: 16px;
}

.footer-text {
  font-size: 12px;
  color: #999;
  margin-top: 20px;
}

.link {
  color: #3CB371;
  text-decoration: underline;
}

/* 登录成功状态的样式 */
.success-card {
  width: 90%;
  max-width: 400px;
  padding: 75px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.success-icon {
  margin-bottom: 20px;
}

.success-text {
  font-size: 20px;
  color: #3CB371;
}
