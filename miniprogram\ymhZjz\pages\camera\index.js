const app = getApp()
Page({

  data: {
    cameraPostion: 'back',
    cameraImg: false,
    photoSrc: '',
    detail: {}
  },


  onLoad: function () {
    this.getEmitData()
  },

  // 接受参数
  getEmitData() {
    const eventChannel = this.getOpenerEventChannel && this.getOpenerEventChannel()
    eventChannel && eventChannel.on('chooseCamera', (data) => {
      this.setData({
        detail: data
      })
    })
  },


  // 反转相机
  reverseCamera() {
    if (this.data.cameraPostion === 'back') {
      this.setData({
        cameraPostion: 'front'
      })
      return
    }
    if (this.data.cameraPostion === 'front') {
      this.setData({
        cameraPostion: 'back'
      })
      return
    }
  },

  // 拍照
  photo() {
    const ctx = wx.createCameraContext()
    ctx.takePhoto({
      quality: 'high',
      success: (res) => {
        this.setData({
          photoSrc: res.tempImagePath,
          cameraImg: true,
        })
      }
    })
  },

  // 去上传抠图编辑
  goEditPhoto() {
    if (this.data.photoSrc) {
      this.Uploadimg(this.data.photoSrc)
    }
  },

  // 返回拍照
  goBackPhoto() {
    this.setData({
      cameraImg: false,
      photoSrc: ''
    })

  },
  //返回前一页
  goPreEdit() {
    this.setData({
      cameraImg: false,
      photoSrc: ''
    })
    wx.navigateBack({
      delta: 1
    })
  },

  // 上传原图
  Uploadimg(filePath) {
    wx.showLoading({
      title: '图像检测中...',
    })
    const fileSizeMB = filePath.size / (1024 * 1024);
    // 检查文件大小
    if (fileSizeMB > 15) {
      wx.showToast({
        title: '图片太大啦，不能超15M哦',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    this.imgUpload(filePath)
  },

  // 上传原图
  imgUpload(filePath) {
    wx.showLoading({
      title: '图片检测中',
    })

    // 使用新的上传方法
    app.uploadFile({
      filePath: filePath,
      name: 'image',
      success: (res) => {
        wx.hideLoading();
        let data = JSON.parse(res.data);
        if (data.code == 200) {
          this.imageDivision(data.data);
        } else if (data.code == 404) {
          wx.showToast({
            title: data.data,
            icon: "none",
          });
        } else {
          wx.navigateTo({
            url: '/pages/login/index',
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        wx.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  imageDivision(imageUrl) {
    wx.showLoading({
      title: '制作中...',
    });

    // 使用新的证件照制作API
    app.request({
      url: 'ai/id-photo-creation',
      method: 'POST',
      data: {
        imageUrl: imageUrl,
        userId: wx.getStorageSync('userId') || 'anonymous'
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.code == 200) {
          // 适配返回数据格式
          const resultData = {
            kimg: res.data.data.segmentedImageUrl, // 抠图后的图片
            oimg: imageUrl, // 原图
            id2: Date.now(), // 临时ID
            ...this.data.detail
          };
          this.goEditPage(resultData);
        } else if (res.data.code == 404) {
          console.log(res.data);
          wx.showToast({
            title: res.data.data,
            icon: 'none'
          });
        } else {
          wx.navigateTo({
            url: '/pages/login/index',
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        wx.showToast({
          title: '制作失败，请重试',
          icon: 'none'
        });
      }
    });
  },



 //去编辑页面
  goEditPage(data) {
    const {
      category,
      heightMm,
      heightPx,
      id,
      name,
      widthMm,
      widthPx,
      isBeautyOn
    } = this.data.detail

    wx.navigateTo({
      url: '/pages/edit/index',
      success: function (res) {
        res.eventChannel.emit('sendImageData', {
          ...data,
          category,
          heightMm,
          heightPx,
          id,
          name,
          widthMm,
          widthPx,
          isBeautyOn
        })
      }
    })
  },


})