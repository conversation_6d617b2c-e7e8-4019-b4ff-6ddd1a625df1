.preEdit {
  height: 100vh;
  background-color: #fff;
  position: relative;
}


.swiper-component {
  margin-top: 4vh;
  width: 100%;
  height: 35vh;
}

.swiper-content {
  width: 100%;
  height: 100%;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover; 
}


.title {
  margin: 30px 20px;
  font-size: 20px;
  font-weight: 500;
}

.block {
  margin: 30px 20px;
  display: flex;
  justify-content: space-between;
}

.dec {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.colorPick {
  display: flex;
}

.circle {
  width: 20px;
  height: 20px;
  margin-left: 10px;
  border-radius: 50%;
}

.rainbow {
  background: linear-gradient(135deg, #ff7f7f, #ffbf80, #ffff80, #80ff80, #80bfff, #bf80ff);
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 45px;
  height: 22px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 22px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2c2c2c;
}

input:checked + .slider:before {
  transform: translateX(23px);
}

.bottom {
  margin: 0 auto;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: 20px;
  padding-bottom: env(safe-area-inset-bottom);
}

.bottom view:first-child {
  width: 40%;
  height: 55px;
  margin-left: 30px;
  background-color: #eee;
  border-radius: 10px;
  font-size: 18px;
  text-align: center;
  line-height: 55px;
}

.bottom view:last-child {
  width: 40%;
  height: 55px;
  margin-right: 30px;
  background-color: #2c2c2c;
  border-radius: 10px;
  color: #fff;
  font-size: 18px;
  text-align: center;
  line-height: 55px;
}
